// TrueBDC CRM Automation Suite - VinSolutions Click to Call
// Specialized click-to-call implementation for VinSolutions CRM system

console.log('VinSo Click-to-Call script loaded!', {
    url: window.location.href,
    timestamp: new Date().toISOString()
});

class VinSoClickToCall {
    constructor(settings = {}) {
        console.log('VinSo Click-to-Call constructor called!', {
            settings,
            url: window.location.href
        });

        this.settings = settings;
        this.isActive = false;
        this.lastClickedNumber = null;
        this.lastClickTime = 0;
        this.clickThreshold = 1000 * 60 * 5; // 5 minutes threshold for consecutive clicks
        this.phoneIcons = new Map();
        this.observer = null;
        this.lastHoveredIcon = null; // For clipboard copy functionality

        this.init();
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            const url = window.location.href;

            TrueBDCUtils.log('Initializing VinSolutions Click to Call', {
                frameContext: frameContext,
                url: url,
                isVinSolutionsPage: this.isVinSolutionsPage()
            });

            // Check if we're on a VinSolutions page
            if (this.isVinSolutionsPage()) {
                this.setupPhoneDetection();
                this.isActive = true;

                TrueBDCUtils.log('VinSolutions Click to Call activated', {
                    frameContext: frameContext,
                    url: url
                });
                TrueBDCUtils.logActivity('vinso_click_to_call_activated', {
                    url: url,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('VinSolutions Click to Call not activated - not VinSolutions page', {
                    url: url,
                    frameContext: frameContext,
                    expectedPatterns: [
                        'vinsolutions.app.coxautoinc.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx',
                        'apps.vinmanager.com/CarDashboard/Pages/CRM/CustomerDashboard.aspx'
                    ]
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize VinSolutions Click to Call', error);
        }
    }

    isVinSolutionsPage() {
        const url = window.location.href;
        const vinSolutionsPatterns = [
            /vinsolutions\.app\.coxautoinc\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i,
            /apps\.vinmanager\.com\/CarDashboard\/Pages\/CRM\/CustomerDashboard\.aspx/i
        ];

        const isMatch = vinSolutionsPatterns.some(pattern => pattern.test(url));

        console.log('VinSo URL Check:', {
            url,
            patterns: vinSolutionsPatterns.map(p => p.toString()),
            isMatch
        });

        return isMatch;
    }

    setupPhoneDetection() {
        // Initial scan for phone numbers
        this.scanForVinSoPhoneNumbers();

        // Set up mutation observer specifically for VinSolutions deal-details container
        this.setupMutationObserver();

        // Periodic scan as fallback (matching Tampermonkey script)
        this.scanInterval = setInterval(() => {
            this.scanForVinSoPhoneNumbers();
        }, 3000); // Every 3 seconds for VinSolutions

        // Stop periodic scanning after 3 minutes to prevent indefinite execution
        setTimeout(() => {
            if (this.scanInterval) {
                clearInterval(this.scanInterval);
                this.scanInterval = null;
            }
        }, 180000); // 3 minutes
    }

    setupMutationObserver() {
        const config = { childList: true, subtree: true };
        const callback = (mutationsList) => {
            for (let mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    // Debounce the scanning to avoid excessive calls
                    clearTimeout(this.scanTimeout);
                    this.scanTimeout = setTimeout(() => {
                        this.scanForVinSoPhoneNumbers();
                    }, 500);
                }
            }
        };

        // Target the deal-details container specifically (from Tampermonkey script)
        const targetNode = document.querySelector('.deal-details');
        if (targetNode) {
            this.observer = new MutationObserver(callback);
            this.observer.observe(targetNode, config);
            TrueBDCUtils.log('VinSolutions mutation observer set up on .deal-details');
        } else {
            // Fallback to document.body if deal-details not found
            this.observer = new MutationObserver(callback);
            this.observer.observe(document.body, config);
            TrueBDCUtils.log('VinSolutions mutation observer set up on document.body (fallback)');

            // Retry finding deal-details container
            setTimeout(() => {
                this.setupMutationObserver();
            }, 3000);
        }
    }

    scanForVinSoPhoneNumbers() {
        try {
            // VinSolutions-specific phone number detection (from Tampermonkey script)
            this.scanVinSolutionsPhoneDetails();
            this.scanGenericPhoneNumbers();

        } catch (error) {
            TrueBDCUtils.error('Error scanning for VinSolutions phone numbers', error);
        }
    }

    scanVinSolutionsPhoneDetails() {
        // Target the actual VinSolutions customer detail elements
        const customerDetailSpans = document.querySelectorAll('span[id*="CustomerDetail"], span.CustomerInfo_CustomerDetail');

        TrueBDCUtils.log('Scanning VinSolutions customer details', {
            foundElements: customerDetailSpans.length,
            elements: Array.from(customerDetailSpans).map(el => ({
                id: el.id,
                className: el.className,
                textContent: el.textContent.substring(0, 100)
            }))
        });

        // Also log to console for easy debugging
        console.log('VinSo Click-to-Call: Found', customerDetailSpans.length, 'customer detail elements');
        customerDetailSpans.forEach((el, index) => {
            console.log(`Element ${index + 1}:`, {
                id: el.id,
                className: el.className,
                textContent: el.textContent.substring(0, 200)
            });
        });

        customerDetailSpans.forEach(span => {
            if (!span.dataset.vinsoConverted) {
                // Mark as processed to avoid re-processing
                span.dataset.vinsoConverted = true;

                // Extract phone numbers from text content
                this.extractPhoneNumbersFromElement(span);
            }
        });

        // Also scan for the old Tampermonkey targets as fallback
        const phoneSpans = document.querySelectorAll('span[analyticsdetect="DealCard|Navigate|Phone"].phone-detail');
        phoneSpans.forEach(span => {
            if (!span.dataset.vinsoConverted) {
                span.dataset.vinsoConverted = true;
                const originalPhoneNumber = span.textContent.trim();
                const cleanNumber = this.cleanPhoneNumber(originalPhoneNumber);
                if (this.isValidPhoneNumber(cleanNumber)) {
                    this.addVinSoClickToCallIcon(span, originalPhoneNumber, cleanNumber);
                }
            }
        });
    }

    extractPhoneNumbersFromElement(element) {
        const text = element.innerHTML;

        // Look for phone numbers in format "H: (*************" and "C: (*************"
        const phonePatterns = [
            /([HC]:|Home:|Cell:|Work:|Mobile:)\s*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/gi,
            /(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})/g
        ];

        phonePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(text)) !== null) {
                const fullMatch = match[0];
                const phoneNumber = match[2] || match[1]; // Get the phone number part
                const cleanNumber = this.cleanPhoneNumber(phoneNumber);

                if (this.isValidPhoneNumber(cleanNumber)) {
                    // Create a wrapper for this specific phone number
                    this.addPhoneNumberToElement(element, phoneNumber, cleanNumber, fullMatch);

                    TrueBDCUtils.log('Found VinSolutions phone number', {
                        fullMatch,
                        phoneNumber,
                        cleanNumber,
                        elementId: element.id
                    });
                }
            }
        });
    }

    addPhoneNumberToElement(element, originalFormat, cleanNumber, fullMatch) {
        // Check if we already added an icon for this number
        const iconId = `vinso-phone-icon-${cleanNumber}`;
        if (document.getElementById(iconId)) return;

        // Replace the phone number text with phone number + icon
        const currentHTML = element.innerHTML;
        const phoneWithIcon = `${fullMatch} <a id="${iconId}" href="tel:+1${cleanNumber}" class="vinso-phone-icon" title="Click to call ${originalFormat}" data-phone="${cleanNumber}" data-original="${originalFormat}" style="cursor: pointer; margin-left: 5px; text-decoration: none; color: inherit; font-size: 14px;">📞</a>`;

        const newHTML = currentHTML.replace(fullMatch, phoneWithIcon);
        element.innerHTML = newHTML;

        // Add click handler to the new icon
        const icon = document.getElementById(iconId);
        if (icon) {
            icon.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleVinSoPhoneClick(cleanNumber, originalFormat, e);
            });

            // Add hover handlers for copy functionality
            icon.addEventListener('mouseover', () => {
                this.lastHoveredIcon = icon;
                document.addEventListener('keydown', this.handleCopy.bind(this));
            });

            icon.addEventListener('mouseout', () => {
                document.removeEventListener('keydown', this.handleCopy.bind(this));
                this.lastHoveredIcon = null;
            });

            this.phoneIcons.set(cleanNumber, icon);
        }
    }



    scanGenericPhoneNumbers() {
        // Generic phone number scanning for VinSolutions pages
        const phonePatterns = [
            /\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b/g, // ************, ************, ************
            /\(\d{3}\)\s?\d{3}[-.\s]?\d{4}/g,     // (*************
            /\b\d{10}\b/g                         // 1234567890
        ];

        const textNodes = this.getTextNodes(document.body);
        
        textNodes.forEach(node => {
            const text = node.textContent;
            phonePatterns.forEach(pattern => {
                const matches = text.match(pattern);
                if (matches) {
                    matches.forEach(match => {
                        const cleanNumber = this.cleanPhoneNumber(match);
                        if (this.isValidPhoneNumber(cleanNumber)) {
                            this.addVinSoClickToCallIcon(node, match, cleanNumber);
                        }
                    });
                }
            });
        });
    }



    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: (node) => {
                    // Skip script and style elements
                    const parent = node.parentElement;
                    if (parent && (parent.tagName === 'SCRIPT' || parent.tagName === 'STYLE')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    // Skip if already processed
                    if (parent && parent.querySelector('.vinso-phone-icon')) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        return textNodes;
    }

    addVinSoClickToCallIcon(targetElement, originalFormat, cleanNumber) {
        try {
            // Check if icon already exists for this number
            const iconId = `vinso-phone-icon-${cleanNumber}`;
            if (document.getElementById(iconId)) return;

            // Create click-to-call icon
            const icon = TrueBDCUtils.createElement('a', {
                id: iconId,
                href: `tel:+1${cleanNumber}`,
                class: 'vinso-phone-icon',
                title: 'Click to call',
                'data-phone': cleanNumber,
                'data-original': originalFormat
            });

            // Use telephone emoji as placeholder (as requested)
            icon.innerHTML = '📞';
            
            // Add styling
            Object.assign(icon.style, {
                cursor: 'pointer',
                marginLeft: '5px',
                textDecoration: 'none',
                color: 'inherit',
                fontSize: '14px'
            });

            // Add click handler
            icon.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleVinSoPhoneClick(cleanNumber, originalFormat, e);
            });

            // Add hover handlers for copy functionality
            icon.addEventListener('mouseover', () => {
                this.lastHoveredIcon = icon;
                document.addEventListener('keydown', this.handleCopy.bind(this));
            });

            icon.addEventListener('mouseout', () => {
                document.removeEventListener('keydown', this.handleCopy.bind(this));
                this.lastHoveredIcon = null;
            });

            // Insert icon after the target element
            if (targetElement.nodeType === Node.TEXT_NODE) {
                targetElement.parentNode.insertBefore(icon, targetElement.nextSibling);
            } else {
                targetElement.appendChild(icon);
            }
            
            this.phoneIcons.set(cleanNumber, icon);

        } catch (error) {
            TrueBDCUtils.error('Error adding VinSolutions click-to-call icon', error);
        }
    }

    handleCopy(e) {
        if (this.lastHoveredIcon && e.ctrlKey && (e.key === 'c' || e.key === 'C')) {
            e.preventDefault();
            const originalFormat = this.lastHoveredIcon.getAttribute('data-original');
            navigator.clipboard.writeText(originalFormat).then(() => {
                const rect = this.lastHoveredIcon.getBoundingClientRect();
                const x = rect.left + window.scrollX + (rect.width / 2);
                const y = rect.top + window.scrollY;
                this.showTooltip(this.lastHoveredIcon, 'Phone Number Copied to Clipboard');
            }).catch(err => {
                TrueBDCUtils.error('Error copying text to clipboard', err);
            });
        }
    }

    async handleVinSoPhoneClick(phoneNumber, originalFormat, event) {
        try {
            const now = Date.now();
            
            // Update click count and check for duplicates
            const shouldProceed = await this.updateClickCount(phoneNumber, event);
            if (!shouldProceed) return;

            // Show tooltip
            this.showTooltip(event.target, `Dialing ${TrueBDCUtils.formatPhoneNumber(phoneNumber)}...`);

            // Initiate call
            window.open(`tel:+1${phoneNumber}`, '_self');

            // Log activity
            TrueBDCUtils.logActivity('vinso_phone_call_initiated', {
                phoneNumber: phoneNumber,
                originalFormat: originalFormat,
                timestamp: new Date().toISOString()
            });

            TrueBDCUtils.log('VinSolutions phone call initiated', { 
                phoneNumber: phoneNumber,
                originalFormat: originalFormat 
            });

        } catch (error) {
            TrueBDCUtils.error('Error handling VinSolutions phone click', error);
            this.showTooltip(event.target, 'Error initiating call', 'error');
        }
    }

    async updateClickCount(phoneNumber, event) {
        const now = Date.now();
        const countKey = `vinso_dialCount_${phoneNumber}`;

        try {
            // Get current count from storage
            const result = await chrome.storage.local.get(countKey);
            let currentCount = parseInt(result[countKey], 10) || 0;

            // Check if this is a consecutive click
            if (phoneNumber !== this.lastClickedNumber ||
                (now - this.lastClickTime) > this.clickThreshold) {
                currentCount = 1;
            } else {
                if (currentCount === 2) {
                    // Show confirmation for 3rd consecutive call
                    const confirmDial = confirm(
                        `You're about to dial this number for the 3rd time in a row. Are you sure you want to dial once more?`
                    );
                    if (!confirmDial) {
                        this.showTooltip(event.target, `Dialing cancelled for ${TrueBDCUtils.formatPhoneNumber(phoneNumber)}.`);
                        return false;
                    }
                }
                currentCount++;
            }

            // Save updated count
            await chrome.storage.local.set({ [countKey]: currentCount });

            // Update tracking variables
            this.lastClickedNumber = phoneNumber;
            this.lastClickTime = now;

            // Show count tooltip
            this.showTooltip(
                event.target,
                `You have dialed ${TrueBDCUtils.formatPhoneNumber(phoneNumber)} ${currentCount} time(s).`
            );

            return true;

        } catch (error) {
            TrueBDCUtils.error('Error updating VinSolutions click count', error);
            return true; // Proceed with call even if count tracking fails
        }
    }

    showTooltip(element, text, type = 'info') {
        const tooltip = TrueBDCUtils.createElement('div', {
            class: 'vinso-tooltip'
        }, {
            position: 'absolute',
            backgroundColor: type === 'error' ? '#dc3545' : '#333',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: '999999',
            whiteSpace: 'nowrap',
            opacity: '0',
            transition: 'opacity 0.3s',
            pointerEvents: 'none'
        });

        tooltip.textContent = text;
        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
        tooltip.style.left = `${rect.left + window.scrollX}px`;
        tooltip.style.opacity = '1';

        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 3000);
    }

    cleanPhoneNumber(phone) {
        if (!phone) return '';
        return phone.replace(/\D/g, '');
    }

    isValidPhoneNumber(cleanNumber) {
        // Must be 10 digits (US phone number)
        return /^\d{10}$/.test(cleanNumber);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('VinSolutions Click to Call settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isVinSolutionsPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Re-scan for phone numbers on new page
                setTimeout(() => {
                    this.scanForVinSoPhoneNumbers();
                }, 1000);
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Stop observing
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            // Clear intervals and timeouts
            if (this.scanInterval) {
                clearInterval(this.scanInterval);
                this.scanInterval = null;
            }

            if (this.scanTimeout) {
                clearTimeout(this.scanTimeout);
                this.scanTimeout = null;
            }

            // Remove all phone icons
            this.phoneIcons.forEach((icon) => {
                if (icon.parentNode) {
                    icon.parentNode.removeChild(icon);
                }
            });
            this.phoneIcons.clear();

            this.isActive = false;

            TrueBDCUtils.log('VinSolutions Click to Call destroyed');
            TrueBDCUtils.logActivity('vinso_click_to_call_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying VinSolutions Click to Call', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /vinsolutions\.app\.coxautoinc\.com|apps\.vinmanager\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isVinSolutionsPage(),
            phoneIconsCount: this.phoneIcons.size,
            lastClickedNumber: this.lastClickedNumber,
            lastClickTime: this.lastClickTime
        };
    }
}

// Make class globally available
window.VinSoClickToCall = VinSoClickToCall;
