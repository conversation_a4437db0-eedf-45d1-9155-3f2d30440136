<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrueBDC CRM Automation</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <img src="icons/icon48.png" alt="TrueBDC Logo">
                <h1>TrueBDC CRM Suite</h1>
            </div>
            <div class="version">v1.0.0</div>
        </header>

        <nav class="tabs">
            <button class="tab-btn active" data-tab="eleads">
                <img src="img/eLeads.png" alt="eLeads" class="tab-icon">
                eLeads
            </button>
            <button class="tab-btn" data-tab="vinso">
                <img src="img/VinSo.png" alt="VinSo" class="tab-icon">
                VinSo
            </button>
            <button class="tab-btn" data-tab="settings">Settings</button>
        </nav>

        <main class="content">
            <!-- eLeads Tab -->
            <div id="eleads-tab" class="tab-content active">
                <div class="script-group">
                    <h3>Core Automation</h3>
                    <!-- Dynamic Tab Title Changer removed - handled by popup title changer using dealership name -->

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Bypass Refresh Confirmation</span>
                            <span class="script-desc">Allows F5 refresh without confirmation dialogs</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="bypass-refresh" data-script="bypassRefresh">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="script-group">
                    <h3>Communication</h3>
                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">TrueBDC Click to Call</span>
                            <span class="script-desc">Adds click-to-call functionality with dial tracking</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="click-to-call" data-script="clickToCall">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Add Calling Text & Time Shortcut</span>
                            <span class="script-desc">Compact modal with tooltip (Ctrl+M: +1min, Ctrl+K: +1min+Complete, Ctrl+Alt+9/Ctrl+Shift+R: Rep modal)</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="calling-text" data-script="callingText">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="script-group">
                    <h3>UI Enhancement</h3>
                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Tab to Popup Converter</span>
                            <span class="script-desc">Converts tabs to popup windows with Ctrl+Alt+9</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="tab-to-popup" data-script="tabToPopup">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Auto Refresh with Timer</span>
                            <span class="script-desc">Simple auto-refresh with modal timer selection (5-10s) and spacebar pause/resume</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="auto-refresh" data-script="autoRefresh">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Auto Navigation</span>
                            <span class="script-desc">Automatically navigate to "Internet Sales Rep" after page refresh</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="auto-navigation" data-script="autoNavigation">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Auto Close Release Notes</span>
                            <span class="script-desc">Automatically closes eLeadCRM release notes popup windows</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="auto-close-release-notes" data-script="autoCloseReleaseNotes">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- VinSo Tab -->
            <div id="vinso-tab" class="tab-content">
                <div class="script-group">
                    <h3>Core Automation</h3>
                    <!-- Dynamic Tab Title Changer removed - handled by popup title changer using dealership name -->

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Bypass Refresh Confirmation</span>
                            <span class="script-desc">Allows F5 refresh without confirmation dialogs</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-bypass-refresh" data-script="bypassRefresh">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="script-group">
                    <h3>Communication</h3>
                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">TrueBDC Click to Call</span>
                            <span class="script-desc">Adds click-to-call functionality with dial tracking</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-click-to-call" data-script="clickToCall">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Add Calling Text & Time Shortcut</span>
                            <span class="script-desc">Compact modal with tooltip (Ctrl+M: +1min, Ctrl+K: +1min+Complete, Ctrl+Alt+9/Ctrl+Shift+R: Rep modal)</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-calling-text" data-script="callingText">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="script-group">
                    <h3>UI Enhancement</h3>
                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Tab to Popup Converter</span>
                            <span class="script-desc">Converts tabs to popup windows with Ctrl+Alt+9</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-tab-to-popup" data-script="tabToPopup">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Auto Refresh with Timer</span>
                            <span class="script-desc">Simple auto-refresh with modal timer selection (5-10s) and spacebar pause/resume</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-auto-refresh" data-script="autoRefresh">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Auto Navigation</span>
                            <span class="script-desc">Automatically navigate to "Internet Sales Rep" after page refresh</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-auto-navigation" data-script="autoNavigation">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="script-item">
                        <div class="script-info">
                            <span class="script-name">Auto Close Release Notes</span>
                            <span class="script-desc">Automatically closes eLeadCRM release notes popup windows</span>
                        </div>
                        <label class="toggle">
                            <input type="checkbox" id="vinso-auto-close-release-notes" data-script="autoCloseReleaseNotes">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="settings-section">
                    <h3>General Settings</h3>
                    <div class="setting-item">
                        <label for="dealership-name">Dealership Name:</label>
                        <input type="text" id="dealership-name" placeholder="Enter dealership name">
                    </div>
                    <div class="setting-item">
                        <label for="agent-name">Agent Name:</label>
                        <input type="text" id="agent-name" placeholder="Enter agent name">
                    </div>
                </div>

                <!-- Airtable Integration - Temporarily Disabled -->
                <div class="settings-section" style="display: none;" id="airtable-section">
                    <h3>Airtable Integration</h3>
                    <div class="setting-item">
                        <label for="airtable-api-key">API Key:</label>
                        <input type="password" id="airtable-api-key" placeholder="Enter Airtable API Key">
                    </div>
                    <div class="setting-item">
                        <label for="airtable-base-id">Base ID:</label>
                        <input type="text" id="airtable-base-id" placeholder="appexR9tFKGHjSWNE">
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Popup Window Settings</h3>
                    <!-- Always on Top feature removed - Chrome extension limitations -->
                    <div class="setting-item">
                        <label for="popup-dimensions">Popup Window Dimensions:</label>
                        <div class="dimension-controls">
                            <div class="dimension-group">
                                <label for="popup-width">Width:</label>
                                <input type="number" id="popup-width" min="400" max="2000" value="1200">
                                <span>px</span>
                            </div>
                            <div class="dimension-group">
                                <label for="popup-height">Height:</label>
                                <input type="number" id="popup-height" min="300" max="1500" value="800">
                                <span>px</span>
                            </div>
                        </div>
                        <div class="current-dimensions" id="current-dimensions">
                            <small>Current saved dimensions will be shown here</small>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Auto Refresh Settings</h3>
                    <div class="setting-item">
                        <label for="refresh-interval">Refresh Interval (seconds):</label>
                        <select id="refresh-interval">
                            <option value="5" selected>5 seconds</option>
                            <option value="6">6 seconds</option>
                            <option value="7">7 seconds</option>
                            <option value="8">8 seconds</option>
                            <option value="9">9 seconds</option>
                            <option value="10">10 seconds</option>
                        </select>
                    </div>
                </div>

                <!-- Auto Navigation settings removed - managed by script toggle only -->

                <div class="settings-actions">
                    <button id="save-settings" class="btn btn-primary">Save Settings</button>
                    <button id="reset-settings" class="btn btn-secondary">Reset to Defaults</button>
                </div>
            </div>


        </main>

        <footer class="footer">
            <div class="status" id="status-message">Ready</div>
            <div class="links">
                <a href="#" id="help-link">Help</a>
                <a href="#" id="feedback-link">Feedback</a>
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
